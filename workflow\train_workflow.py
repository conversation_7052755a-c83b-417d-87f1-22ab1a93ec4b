#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import os
import time
import random
from agent_ppo.feature.definition import (
    sample_process,
    build_frame,
    FrameCollector,
    NONE_ACTION,
)
from kaiwu_agent.utils.common_func import attached
from agent_ppo.conf.conf import GameConfig
from .env_conf_manager_self import EnvConfManager
from tools.model_pool_utils import get_valid_model_pool
from tools.metrics_utils import get_training_metrics


@attached
def workflow(envs, agents, logger=None, monitor=None, *args, **kwargs):
    # Whether the agent is training, corresponding to do_predicts
    # 智能体是否进行训练
    do_learns = [True, True]
    last_save_model_time = time.time()

    # Create environment configuration manager instance
    # 创建对局配置管理器实例
    env_conf_manager = EnvConfManager(
        config_path="agent_ppo/conf/train_env_conf.toml",
        logger=logger,
    )

    # Create EpisodeRunner instance
    # 创建 EpisodeRunner 实例
    episode_runner = EpisodeRunner(
        env=envs[0],
        agents=agents,
        logger=logger,
        monitor=monitor,
        env_conf_manager=env_conf_manager,
    )

    while True:
        # Run episodes and collect data
        # 运行对局并收集数据
        for g_data in episode_runner.run_episodes():
            for index, (d_learn, agent) in enumerate(zip(do_learns, agents)):
                if d_learn and len(g_data[index]) > 0:
                    # The learner trains in a while true loop, here learn actually sends samples
                    # learner 采用 while true 训练，此处 learn 实际为发送样本
                    agent.learn(g_data[index])
            g_data.clear()

            now = time.time()
            if now - last_save_model_time > GameConfig.MODEL_SAVE_INTERVAL:
                agents[0].save_model()
                last_save_model_time = now


class EpisodeRunner:
    def __init__(self, env, agents, logger, monitor, env_conf_manager):
        self.env = env
        self.agents = agents
        self.logger = logger
        self.monitor = monitor
        self.env_conf_manager = env_conf_manager
        self.agent_num = len(agents)
        self.episode_cnt = 0
        self.predict_success_count = 0
        self.load_model_success_count = 0
        self.last_report_monitor_time = 0

        # 覆盖优先的对阵调度器状态
        self.available_heroes = [169, 173, 174]  
        n = len(self.available_heroes)
        # 3×3 对阵计数矩阵，行=蓝方英雄索引，列=红方英雄索引
        self.pair_counts = [[0 for _ in range(n)] for _ in range(n)]
        self.side_flip = False   # 每局切换一次先后手，消除蓝红偏置
        self.last_pair = None    # 避免连续两局完全相同的对阵

    def run_episodes(self):
        # Single environment process
        # 单局流程
        while True:
            # Retrieving training metrics
            training_metrics = get_training_metrics()
            if training_metrics:
                for key, value in training_metrics.items():
                    if key == "env":
                        for env_key, env_value in value.items():
                            self.logger.info(f"training_metrics {key} {env_key} is {env_value}")
                    else:
                        self.logger.info(f"training_metrics {key} is {value}")

            # Update environment configuration
            usr_conf, is_eval, monitor_side = self.env_conf_manager.update_config()
            
            # Random hero selection for selfplay
            if self.env_conf_manager.get_opponent_agent() == "selfplay":
                usr_conf = self._randomize_heroes(usr_conf)

            # Start a new environment
            observation, extra_info = self.env.reset(usr_conf=usr_conf)
            if self._handle_disaster_recovery(extra_info):
                break
            
            # Reset agents
            self.reset_agents(observation)

            # Reset environment frame collector
            frame_collector = FrameCollector(self.agent_num)

            # Game variables
            self.episode_cnt += 1
            frame_no = 0
            reward_sum_list = [0] * self.agent_num
            is_train_test = os.environ.get("is_train_test", "False").lower() == "true"
            self.logger.info(f"Episode {self.episode_cnt} start, usr_conf is {usr_conf}")

            # Reward initialization
            for i, (do_sample, agent) in enumerate(zip(self.do_samples, self.agents)):
                if do_sample:
                    reward = agent.reward_manager.result(observation[i]["frame_state"])
                    observation[i]["reward"] = reward
                    total_reward = (reward.get("farm_reward", 0) + reward.get("kda_reward", 0) +
                                  reward.get("hp_reward", 0) + reward.get("tower_reward", 0))
                    reward_sum_list[i] += total_reward

            while True:
                # Initialize the default actions
                actions = [NONE_ACTION] * self.agent_num

                for index, (do_predict, do_sample, agent) in enumerate(
                    zip(self.do_predicts, self.do_samples, self.agents)
                ):
                    if do_predict:
                        if not is_eval:
                            actions[index] = agent.predict(observation[index])
                        else:
                            actions[index] = agent.exploit(observation[index])
                        self.predict_success_count += 1

                        # Only sample when do_sample=True and is_eval=False
                        if not is_eval and do_sample:
                            frame = build_frame(agent, observation[index])
                            frame_collector.save_frame(frame, agent_id=index)

                # Step forward
                frame_no, observation, terminated, truncated, extra_info = self.env.step(actions)
                if self._handle_disaster_recovery(extra_info):
                    break

                # Reward generation
                for i, (do_sample, agent) in enumerate(zip(self.do_samples, self.agents)):
                    if do_sample:
                        reward = agent.reward_manager.result(observation[i]["frame_state"])
                        observation[i]["reward"] = reward
                        reward_sum_list[i] += reward["reward_sum"]

                now = time.time()
                if now - self.last_report_monitor_time >= 60:
                    monitor_data = {
                        "actor_predict_succ_cnt": self.predict_success_count,
                        "actor_load_last_model_succ_cnt": self.load_model_success_count,
                    }
                    self.monitor.put_data({os.getpid(): monitor_data})
                    self.last_report_monitor_time = now

                keys_to_exclude = {'hero_states', 'npc_states'}
                frame_state_for_log = {
                    key: value
                    for key, value in observation[0]['frame_state'].items()
                    if key not in keys_to_exclude
                }

                # Normal end or timeout exit
                is_train_test = os.environ.get("is_train_test", "False").lower() == "true"
                is_gameover = terminated or truncated or (is_train_test and frame_no >= 1000)
                if is_gameover:
                    self.logger.info(
                        f"episode_{self.episode_cnt} terminated in fno_{frame_no}, truncated:{truncated}, eval:{is_eval}, reward_sum:{reward_sum_list[monitor_side]}"
                    )
                    # Save last reward
                    for i, (do_sample, agent) in enumerate(zip(self.do_samples, self.agents)):
                        if not is_eval and do_sample:
                            reward_data = observation[i]["reward"]
                            frame_collector.save_last_frame(
                                agent_id=i,
                                reward=reward_data["reward_sum"],
                                economy_reward=reward_data.get("economy_reward", 0),
                                combat_reward=reward_data.get("combat_reward", 0),
                                survival_reward=reward_data.get("survival_reward", 0),
                                building_reward=reward_data.get("building_reward", 0),
                            )

                    monitor_data = {}
                    if self.monitor and is_eval:
                        monitor_data["reward"] = round(reward_sum_list[monitor_side], 2)
                        self.monitor.put_data({os.getpid(): monitor_data})

                    # Sample process
                    if len(frame_collector) > 0 and not is_eval:
                        list_agents_samples = sample_process(frame_collector)
                        yield list_agents_samples
                    break

    def reset_agents(self, observation):
        opponent_agent = self.env_conf_manager.get_opponent_agent()
        monitor_side = self.env_conf_manager.get_monitor_side()
        is_train_test = os.environ.get("is_train_test", "False").lower() == "true"

        # who predicts / who samples
        self.do_predicts = [True, True]
        self.do_samples = [True, True]

        # Load model according to the configuration
        for i, agent in enumerate(self.agents):
            if i == monitor_side:
                agent.load_model(id="latest")
            else:
                if opponent_agent == "common_ai":
                    self.do_predicts[i] = False
                    self.do_samples[i] = False
                elif opponent_agent == "selfplay":
                    agent.load_model(id="latest")
                    self.load_model_success_count += 1
                else:
                    eval_candidate_model = get_valid_model_pool(self.logger)
                    if int(opponent_agent) not in eval_candidate_model:
                        raise Exception(f"opponent_agent model_id {opponent_agent} not in {eval_candidate_model}")
                    else:
                        if is_train_test:
                            self.logger.info(f"Run train_test, cannot get opponent agent, so replace with latest model")
                            agent.load_model(id="latest")
                        else:
                            if is_train_test:
                                self.logger.info(f"Run train_test, cannot get opponent agent, so replace with latest model")
                                agent.load_model(id="latest")
                            else:
                                agent.load_opponent_agent(id=opponent_agent)
                            self.do_samples[i] = False
            agent.reset(observation[i])

    def _randomize_heroes(self, usr_conf):
        """
        覆盖优先 + 交替换边 的自对弈英雄调度：
        - 优先选择当前出现次数最少的对阵 (i, j)
        - 候选中随机挑一个，保持探索
        - 每局交换阵营（蓝红互换），消除先后手偏置
        """
        heroes = self.available_heroes
        n = len(heroes)

        # 找到计数最少的对阵集合
        min_count = min(min(row) for row in self.pair_counts)
        candidates = [(i, j) for i in range(n) for j in range(n) if self.pair_counts[i][j] == min_count]

        # 避免与上一局完全相同的 (i,j) 连续重复（有别的候选时才避免）
        if self.last_pair in candidates and len(candidates) > 1:
            candidates = [p for p in candidates if p != self.last_pair]

        i, j = random.choice(candidates)  # 选择对阵索引
        self.pair_counts[i][j] += 1
        self.last_pair = (i, j)

        blue_hero = heroes[i]
        red_hero = heroes[j]

        # 每局切换一次先后手，保证镜像公平
        if self.side_flip:
            blue_hero, red_hero = red_hero, blue_hero
        self.side_flip = not self.side_flip

        # 写回 usr_conf
        if "lineups" in usr_conf:
            if "blue_camp" in usr_conf["lineups"] and len(usr_conf["lineups"]["blue_camp"]) > 0:
                usr_conf["lineups"]["blue_camp"][0]["hero_id"] = blue_hero
            if "red_camp" in usr_conf["lineups"] and len(usr_conf["lineups"]["red_camp"]) > 0:
                usr_conf["lineups"]["red_camp"][0]["hero_id"] = red_hero

        # 可选：每隔一段时间打印一次覆盖情况
        if (self.episode_cnt % 50) == 1:
            pc = self.pair_counts
            self.logger.info(f"pair_counts={pc}, chosen(blue,red)=({blue_hero},{red_hero})")

        return usr_conf

    def _handle_disaster_recovery(self, extra_info):
        result_code = extra_info.get("result_code", 0)
        result_message = extra_info.get("result_message", "")
        if result_code < 0:
            self.logger.error(f"Env run error, result_code: {result_code}, result_message: {result_message}")
            raise RuntimeError(result_message)
        elif result_code > 0:
            return True
        return False