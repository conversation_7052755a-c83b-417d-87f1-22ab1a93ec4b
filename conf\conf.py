#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""




class GameConfig:
    # Set the weight of each reward item and use it in reward_manager
    # 设置各个回报项的权重，在reward_manager中使用
    REWARD_WEIGHT_DICT = {
        "hp_point": 2.0,
        "tower_hp_point": 10.0,
        "money": 0.006,
        "exp": 0.006,
        "ep_rate": 0.75,
        "death": -1.0,
        "kill": -0.6,
        "last_hit": 0.5,
        "forward": 0.01,
    }
    # Time decay factor, used in reward_manager
    # 时间衰减因子，在reward_manager中使用
    TIME_SCALE_ARG = 10000
    # Model save interval configuration, used in workflow
    # 模型保存间隔配置，在workflow中使用
    MODEL_SAVE_INTERVAL = 1800



# Dimension configuration, used when building the model
# 维度配置，构建模型时使用
class DimConfig:
    DIM_OF_HERO_FRD = [81]
    DIM_OF_HERO_EMY = [81]
    DIM_OF_HERO_MAIN = [7]
    DIM_OF_SOLDIER_1_10 = [15, 15, 15, 15]
    DIM_OF_SOLDIER_11_20 = [15, 15, 15, 15]
    DIM_OF_ORGAN_1_2 = [10]
    DIM_OF_ORGAN_3_4 = [10]
    DIM_OF_GLOBAL_INFO = [22]


# Configuration related to model and algorithms used
# 模型和算法使用的相关配置
class Config:  
    NETWORK_NAME = "network"
    LSTM_TIME_STEPS = 16
    LSTM_UNIT_SIZE = 512
    DATA_SPLIT_SHAPE = [
        331 + 85,
        1,  # reward_sum
        1,  # advantage
        1,  # economy_reward_sum
        1,  # combat_reward_sum
        1,  # survival_reward_sum
        1,  # building_reward_sum
        1,
        1,
        1,
        1,
        12,
        16,
        16,
        16,
        16,
        9,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        LSTM_UNIT_SIZE,
        LSTM_UNIT_SIZE,
    ]
    SERI_VEC_SPLIT_SHAPE = [(331,), (85,)]  
    INIT_LEARNING_RATE_START = 0.00004
    TARGET_LR = 0.00004
    TARGET_STEP = 200000
    BETA_START = 0.025
    LOG_EPSILON = 1e-6
    LABEL_SIZE_LIST = [12, 16, 16, 16, 16, 9]
    IS_REINFORCE_TASK_LIST = [
        True,
        True,
        True,
        True,
        True,
        True,
    ]

    CLIP_PARAM = 0.2

    MIN_POLICY = 0.00001

    TARGET_EMBED_DIM = 32

    data_shapes = [
        [(331 + 85) * 16],
        [16],  # reward_sum
        [16],  # advantage
        [16],  # economy_reward_sum
        [16],  # combat_reward_sum
        [16],  # survival_reward_sum
        [16],  # building_reward_sum
        [16],
        [16],
        [16],
        [16],
        [192],
        [256],
        [256],
        [256],
        [256],
        [144],
        [16],
        [16],
        [16],
        [16],
        [16],
        [16],
        [16],
        [512],
        [512],
    ]

    LEGAL_ACTION_SIZE_LIST = LABEL_SIZE_LIST.copy()
    LEGAL_ACTION_SIZE_LIST[-1] = LEGAL_ACTION_SIZE_LIST[-1] * LEGAL_ACTION_SIZE_LIST[0]

    GAMMA = 0.995
    LAMDA = 0.95

    USE_GRAD_CLIP = True
    GRAD_CLIP_RANGE = 0.5

    # The input dimension of samples on the learner from Reverb varies depending on the algorithm used.
    # learner上reverb样本的输入维度, 注意不同的算法维度不一样
    SAMPLE_DIM = sum(DATA_SPLIT_SHAPE[:-2]) * LSTM_TIME_STEPS + sum(DATA_SPLIT_SHAPE[-2:])
