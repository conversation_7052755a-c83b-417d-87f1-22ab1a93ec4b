#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from kaiwu_agent.utils.common_func import create_cls, Frame, attached
import numpy as np
import collections
from agent_ppo.conf.conf import Config

SampleData = create_cls("SampleData", npdata=None)

# 扩展Frame来支持多头价值和奖励
MultiHeadFrame = create_cls(
    "MultiHeadFrame",
    frame_no=None,
    feature=None,
    legal_action=None,
    action=None,
    reward=None,
    reward_sum=0,
    value=None,
    next_value=0,
    advantage=0,
    # 多头奖励
    economy_reward=0,
    combat_reward=0,
    survival_reward=0,
    building_reward=0,
    # 多头价值
    economy_value=None,
    combat_value=None,
    survival_value=None,
    building_value=None,
    # 多头next_value
    economy_next_value=0,
    combat_next_value=0,
    survival_next_value=0,
    building_next_value=0,
    # 多头advantage
    economy_advantage=0,
    combat_advantage=0,
    survival_advantage=0,
    building_advantage=0,
    # 多头reward_sum
    economy_reward_sum=0,
    combat_reward_sum=0,
    survival_reward_sum=0,
    building_reward_sum=0,
    prob=None,
    sub_action=None,
    lstm_info=None,
    is_train=False,
)

ObsData = create_cls("ObsData", feature=None, legal_action=None, lstm_cell=None, lstm_hidden=None)

# ActData needs to contain d_action and d_prob, used for visualization
# ActData 需要包含 d_action 和 d_prob, 用于可视化智能体预测概率
ActData = create_cls(
    "ActData",
    action=None,
    d_action=None,
    prob=None,
    d_prob=None,
    value=None,
    economy_value=None,
    combat_value=None,
    survival_value=None,
    building_value=None,
    lstm_cell=None,
    lstm_hidden=None,
)

NONE_ACTION = [0, 15, 15, 15, 15, 0]


@attached
def sample_process(collector):
    return collector.sample_process()


# Create the sample for the current frame
# 创建当前帧的样本
def build_frame(agent, observation):
    obs_data, act_data = agent.obs_data, agent.act_data
    is_train = False
    frame_state = observation["frame_state"]
    hero_list = frame_state["hero_states"]
    frame_no = frame_state["frameNo"]
    for hero in hero_list:
        hero_camp = hero["actor_state"]["camp"]
        hero_hp = hero["actor_state"]["hp"]
        if hero_camp == agent.hero_camp:
            is_train = True if hero_hp > 0 else False

    #frame_state['npc_states'][0] 44 46 1113 1114藍紅防禦塔 1113 1114 藍紅水晶
    if obs_data.feature is not None:
        feature_vec = np.array(obs_data.feature) #len=10
    else:
        feature_vec = np.array(observation["observation"])
    #main_hero frame_state['hero_states'][0] frame_state['hero_states'][1]
    reward = observation["reward"]["reward_sum"]

    # 获取多头奖励
    economy_reward = observation["reward"].get("economy_reward", 0)
    combat_reward = observation["reward"].get("combat_reward", 0)
    survival_reward = observation["reward"].get("survival_reward", 0)
    building_reward = observation["reward"].get("building_reward", 0)

    sub_action_mask = observation["sub_action_mask"]

    prob, value, action = act_data.prob, act_data.value, act_data.action
    lstm_cell, lstm_hidden = act_data.lstm_cell, act_data.lstm_hidden

    legal_action = _update_legal_action(observation["legal_action"], action)
    frame = MultiHeadFrame(
        frame_no=frame_no,
        feature=feature_vec.reshape([-1]),
        legal_action=legal_action.reshape([-1]),
        action=action,
        reward=reward,
        reward_sum=0,
        value=value.flatten()[0],
        next_value=0,
        advantage=0,
        # 多头奖励
        economy_reward=economy_reward,
        combat_reward=combat_reward,
        survival_reward=survival_reward,
        building_reward=building_reward,
        # 多头价值（推理时为None，训练时会设置）
        economy_value=act_data.economy_value.flatten()[0] if act_data.economy_value is not None else None,
        combat_value=act_data.combat_value.flatten()[0] if act_data.combat_value is not None else None,
        survival_value=act_data.survival_value.flatten()[0] if act_data.survival_value is not None else None,
        building_value=act_data.building_value.flatten()[0] if act_data.building_value is not None else None,
        # 其他多头字段初始化为0
        economy_next_value=0,
        combat_next_value=0,
        survival_next_value=0,
        building_next_value=0,
        economy_advantage=0,
        combat_advantage=0,
        survival_advantage=0,
        building_advantage=0,
        economy_reward_sum=0,
        combat_reward_sum=0,
        survival_reward_sum=0,
        building_reward_sum=0,
        prob=prob,
        sub_action=sub_action_mask[action[0]],
        lstm_info=np.concatenate([lstm_cell.flatten(), lstm_hidden.flatten()]).reshape([-1]),
        is_train=False if action[0] < 0 else is_train,
    )
    return frame


# Construct legal_action based on the actual action taken
# 根据实际采用的action，构建legal_action
def _update_legal_action(original_la, action):
    target_size = Config.LABEL_SIZE_LIST[-1]
    top_size = Config.LABEL_SIZE_LIST[0]
    original_la = np.array(original_la)
    fix_part = original_la[: -target_size * top_size]
    target_la = original_la[-target_size * top_size :]
    target_la = target_la.reshape([top_size, target_size])[action[0]]
    return np.concatenate([fix_part, target_la], axis=0)


class FrameCollector:
    def __init__(self, num_agents):
        self._data_shapes = Config.data_shapes
        self._LSTM_FRAME = Config.LSTM_TIME_STEPS

        self.num_agents = num_agents
        self.rl_data_map = [collections.OrderedDict() for _ in range(num_agents)]
        self.m_replay_buffer = [[] for _ in range(num_agents)]

        self.gamma = Config.GAMMA
        self.lamda = Config.LAMDA

    def reset(self, num_agents):
        self.num_agents = num_agents
        self.rl_data_map = [collections.OrderedDict() for _ in range(self.num_agents)]
        self.m_replay_buffer = [[] for _ in range(self.num_agents)]

    def save_frame(self, rl_data_info, agent_id):
        # samples must saved by frame_no order
        # 样本必须按帧号顺序保存
        reward = self._clip_reward(rl_data_info.reward)

        # 处理多头奖励
        economy_reward = self._clip_reward(rl_data_info.economy_reward)
        combat_reward = self._clip_reward(rl_data_info.combat_reward)
        survival_reward = self._clip_reward(rl_data_info.survival_reward)
        building_reward = self._clip_reward(rl_data_info.building_reward)

        if len(self.rl_data_map[agent_id]) > 0:
            last_key = list(self.rl_data_map[agent_id].keys())[-1]
            last_rl_data_info = self.rl_data_map[agent_id][last_key]
            last_rl_data_info.next_value = rl_data_info.value
            last_rl_data_info.reward = reward

            # 设置多头next_value
            if hasattr(rl_data_info, 'economy_value') and rl_data_info.economy_value is not None:
                last_rl_data_info.economy_next_value = rl_data_info.economy_value
                last_rl_data_info.combat_next_value = rl_data_info.combat_value
                last_rl_data_info.survival_next_value = rl_data_info.survival_value
                last_rl_data_info.building_next_value = rl_data_info.building_value

            # 设置多头奖励
            last_rl_data_info.economy_reward = economy_reward
            last_rl_data_info.combat_reward = combat_reward
            last_rl_data_info.survival_reward = survival_reward
            last_rl_data_info.building_reward = building_reward

        rl_data_info.reward = 0
        rl_data_info.economy_reward = 0
        rl_data_info.combat_reward = 0
        rl_data_info.survival_reward = 0
        rl_data_info.building_reward = 0
        self.rl_data_map[agent_id][rl_data_info.frame_no] = rl_data_info

    def save_last_frame(self, reward, agent_id, economy_reward=0, combat_reward=0, survival_reward=0, building_reward=0):
        if len(self.rl_data_map[agent_id]) > 0:
            last_key = list(self.rl_data_map[agent_id].keys())[-1]
            last_rl_data_info = self.rl_data_map[agent_id][last_key]
            last_rl_data_info.next_value = 0
            last_rl_data_info.reward = reward

            # 设置多头next_value为0
            last_rl_data_info.economy_next_value = 0
            last_rl_data_info.combat_next_value = 0
            last_rl_data_info.survival_next_value = 0
            last_rl_data_info.building_next_value = 0

            # 设置多头奖励
            last_rl_data_info.economy_reward = economy_reward
            last_rl_data_info.combat_reward = combat_reward
            last_rl_data_info.survival_reward = survival_reward
            last_rl_data_info.building_reward = building_reward

    def sample_process(self):
        self._calc_reward()
        self._format_data()
        return self.m_replay_buffer

    def _calc_reward(self):
        """
        Calculate cumulated reward and advantage with GAE for multi-head values.
        reward_sum: used for value loss
        advantage: used for policy loss
        V(s) here is a approximation of target network
        """
        """
        计算累积奖励和优势函数（GAE）用于多头价值。
        reward_sum: 用于值损失
        advantage: 用于策略损失
        V(s) 这里是目标网络的近似值
        """
        for i in range(self.num_agents):
            reversed_keys = list(self.rl_data_map[i].keys())
            reversed_keys.reverse()

            # 总GAE计算
            gae, last_gae = 0.0, 0.0
            # 多头GAE计算
            economy_gae, combat_gae, survival_gae, building_gae = 0.0, 0.0, 0.0, 0.0

            for j in reversed_keys:
                rl_info = self.rl_data_map[i][j]

                # 总GAE计算（保持原有逻辑）
                delta = -rl_info.value + rl_info.reward + self.gamma * rl_info.next_value
                gae = gae * self.gamma * self.lamda + delta
                rl_info.advantage = gae
                rl_info.reward_sum = gae + rl_info.value

                # 多头GAE计算（仅在有多头价值时计算）
                if hasattr(rl_info, 'economy_value') and rl_info.economy_value is not None:
                    # 经济头GAE
                    economy_delta = -rl_info.economy_value + rl_info.economy_reward + self.gamma * rl_info.economy_next_value
                    economy_gae = economy_gae * self.gamma * self.lamda + economy_delta
                    rl_info.economy_advantage = economy_gae
                    rl_info.economy_reward_sum = economy_gae + rl_info.economy_value

                    # 战斗头GAE
                    combat_delta = -rl_info.combat_value + rl_info.combat_reward + self.gamma * rl_info.combat_next_value
                    combat_gae = combat_gae * self.gamma * self.lamda + combat_delta
                    rl_info.combat_advantage = combat_gae
                    rl_info.combat_reward_sum = combat_gae + rl_info.combat_value

                    # 生存头GAE
                    survival_delta = -rl_info.survival_value + rl_info.survival_reward + self.gamma * rl_info.survival_next_value
                    survival_gae = survival_gae * self.gamma * self.lamda + survival_delta
                    rl_info.survival_advantage = survival_gae
                    rl_info.survival_reward_sum = survival_gae + rl_info.survival_value

                    # 建筑头GAE
                    building_delta = -rl_info.building_value + rl_info.building_reward + self.gamma * rl_info.building_next_value
                    building_gae = building_gae * self.gamma * self.lamda + building_delta
                    rl_info.building_advantage = building_gae
                    rl_info.building_reward_sum = building_gae + rl_info.building_value

    # For every LSTM_TIME_STEPS samples, concatenate 1 LSTM state
    # 每LSTM_TIME_STEPS个样本，需要拼接1个lstm状态
    def _reshape_lstm_batch_sample(self, sample_batch, sample_lstm):
        sample = np.zeros([np.prod(sample_batch.shape) + np.prod(sample_lstm.shape)])
        idx, s_idx = 0, 0

        sample[-sample_lstm.shape[0] :] = sample_lstm
        for split_shape in self._data_shapes[:-2]:
            one_shape = split_shape[0] // self._LSTM_FRAME
            sample[s_idx : s_idx + split_shape[0]] = sample_batch[:, idx : idx + one_shape].reshape([-1])
            idx += one_shape
            s_idx += split_shape[0]
        return SampleData(npdata=sample.astype(np.float32))

    # Create the sample for the current frame
    # 根据LSTM_TIME_STEPS，组合送入样本池的样本
    def _format_data(self):
        sample_one_size = np.sum(self._data_shapes[:-2]) // self._LSTM_FRAME
        sample_lstm_size = np.sum(self._data_shapes[-2:])
        sample_batch = np.zeros([self._LSTM_FRAME, sample_one_size])
        first_frame_no = -1

        for i in range(self.num_agents):
            sample_lstm = np.zeros([sample_lstm_size])
            cnt = 0
            for j in self.rl_data_map[i]:
                rl_info = self.rl_data_map[i][j]
                if cnt == 0:
                    first_frame_no = rl_info.frame_no

                idx, dlen = 0, 0

                dlen = rl_info.feature.shape[0]
                sample_batch[cnt, idx : idx + dlen] = rl_info.feature
                idx += dlen

                dlen = rl_info.legal_action.shape[0]
                sample_batch[cnt, idx : idx + dlen] = rl_info.legal_action
                idx += dlen

                sample_batch[cnt, idx] = rl_info.reward_sum
                idx += 1
                sample_batch[cnt, idx] = rl_info.advantage
                idx += 1

                dlen = 6
                sample_batch[cnt, idx : idx + dlen] = rl_info.action
                idx += dlen

                for p in rl_info.prob:
                    dlen = len(p)
                    sample_batch[cnt, idx : idx + dlen] = p
                    idx += dlen

                dlen = 6
                sample_batch[cnt, idx : idx + dlen] = rl_info.sub_action
                idx += dlen

                sample_batch[cnt, idx] = rl_info.is_train
                idx += 1

                assert idx == sample_one_size, "Sample check failed, {}/{}".format(idx, sample_one_size)

                cnt += 1
                if cnt == self._LSTM_FRAME:
                    cnt = 0
                    sample = self._reshape_lstm_batch_sample(sample_batch, sample_lstm)
                    self.m_replay_buffer[i].append(sample)
                    sample_lstm = rl_info.lstm_info

    def _clip_reward(self, reward, max=100, min=-100):
        if reward > max:
            reward = max
        elif reward < min:
            reward = min
        return reward

    def __len__(self):
        return max([len(agent_samples) for agent_samples in self.rl_data_map])


@attached
def SampleData2NumpyData(g_data):
    return g_data.npdata


@attached
def NumpyData2SampleData(s_data):
    return SampleData(npdata=s_data)
