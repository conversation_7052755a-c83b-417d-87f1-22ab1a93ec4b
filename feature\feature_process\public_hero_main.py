#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from enum import Enum
from .feature_normalizer import FeatureNormalizer
import configparser
import os
import math
from collections import OrderedDict
import numpy as np


class PublicHeroMainProcess:
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.main_hero_info = None

    def cal_dist(self, pos1, pos2):
        dist = math.sqrt((pos1["x"] / 100.0 - pos2["x"] / 100.0) ** 2 + (pos1["z"] / 100.0 - pos2["z"] / 100.0) ** 2)
        return dist

    def generate_hero_info_list(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        for hero in frame_state["hero_states"]:
            if hero["actor_state"]["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                self.main_hero_info = hero
            else:
                self.enemy_camp_hero_dict[hero["actor_state"]["config_id"]] = hero

    def process_vec_public_hero_main(self, frame_state):
        """處理公共英雄主特徵"""
        self.generate_hero_info_list(frame_state)

        # 默認值：當沒有活著的英雄時返回全0的13維特徵
        default_feature = [0.0] * 7

        # 生成我方英雄的敵方相關特徵
        for hero in self.main_camp_hero_dict.values():
            if hero["actor_state"]["hp"] > 0:
                # 最近敵方子彈位置特徵
                bullet_x = self.cur_location_of_nearest_enemy_bullet_of_enemy_1_x_diff(hero, frame_state)
                bullet_z = self.cur_location_of_nearest_enemy_bullet_of_enemy_1_z_diff(hero, frame_state)
                bullet_dist = self.cur_location_of_nearest_enemy_bullet_of_enemy_1_distance(hero, frame_state)

                # 敵方英雄攻擊範圍特徵
                enemy_common = self.enemy_1_in_main_hero_common_atk_range(hero, frame_state)
                #enemy_skill1 = self.enemy_1_in_main_hero_skill_1_atk_range(hero, frame_state)
                #enemy_skill2 = self.enemy_1_in_main_hero_skill_2_atk_range(hero, frame_state)
                #enemy_skill3 = self.enemy_1_in_main_hero_skill_3_atk_range(hero, frame_state)

                # 敵方單位攻擊範圍特徵
                enemy_soldier = self.enemy_soldier_under_main_heros_atk(hero, frame_state)
                enemy_tower = self.enemy_tower_under_main_heros_atk(hero, frame_state)

                # 合併所有特徵
                local_vector_feature = bullet_x + bullet_z + bullet_dist + enemy_common + enemy_soldier + enemy_tower
                local_vector_feature.extend(self.get_dist_from_all_heros(frame_state))
                return local_vector_feature

        # 如果沒有找到活著的英雄，返回默認特徵
        return default_feature
    

    def get_dist_from_all_heros(self, frame_state):
        """英雄与敌方英雄的距离"""
        characteristic_distance = 300
        k_decay = 1.0 / (characteristic_distance ** 2)
        dist = self.cal_dist(frame_state['hero_states'][0]['actor_state']['location'], frame_state['hero_states'][1]['actor_state']['location'])
        normalized_dist = np.exp(-k_decay * (dist ** 2))
        #print(normalized_dist)
        return [normalized_dist]

    def cur_location_of_nearest_enemy_bullet_of_enemy_1_x_diff(self, hero, frame_state):
        """最近敵方子彈的X座標差"""
        if hero["actor_state"]["hp"] <= 0:
            return [0.0]

        hero_location = hero["actor_state"]["location"]
        nearest_bullet = self._find_nearest_enemy_bullet(hero, frame_state)

        if nearest_bullet:
            bullet_location = nearest_bullet["location"]
            x_diff = bullet_location["x"] - hero_location["x"]

            # 處理陣營轉換
            if self.transform_camp2_to_camp1:
                x_diff = -x_diff

            # 歸一化到[-1, 1]範圍
            value = self.normalizer.min_max(x_diff, -30000.0, 30000.0)
            return [value]
        else:
            return [0.0]

    def cur_location_of_nearest_enemy_bullet_of_enemy_1_z_diff(self, hero, frame_state):
        """最近敵方子彈的Z座標差"""
        if hero["actor_state"]["hp"] <= 0:
            return [0.0]

        hero_location = hero["actor_state"]["location"]
        nearest_bullet = self._find_nearest_enemy_bullet(hero, frame_state)

        if nearest_bullet:
            bullet_location = nearest_bullet["location"]
            z_diff = bullet_location["z"] - hero_location["z"]

            # 處理陣營轉換
            if self.transform_camp2_to_camp1:
                z_diff = -z_diff

            # 歸一化到[-1, 1]範圍
            value = self.normalizer.min_max(z_diff, -30000.0, 30000.0)
            return [value]
        else:
            return [0.0]

    def cur_location_of_nearest_enemy_bullet_of_enemy_1_distance(self, hero, frame_state):
        """最近敵方子彈的距離"""
        if hero["actor_state"]["hp"] <= 0:
            return [0.0]

        hero_location = hero["actor_state"]["location"]
        nearest_bullet = self._find_nearest_enemy_bullet(hero, frame_state)

        if nearest_bullet:
            bullet_location = nearest_bullet["location"]
            distance = self.cal_dist(hero_location, bullet_location)
            # 歸一化到[0, 1]範圍
            #print(f"nearest Bullet: ", distance)
            value = self.normalizer.min_max(distance, 0, 600.0)
            return [value]
        else:
            return [0.0]

    def enemy_1_in_main_hero_common_atk_range(self, hero, frame_state):
        """敵方英雄是否在普通攻擊範圍內"""
        return self._check_enemy_in_range(hero, frame_state, "common")

    def enemy_1_in_main_hero_skill_1_atk_range(self, hero, frame_state):
        """敵方英雄是否在技能1攻擊範圍內"""
        return self._check_enemy_in_range(hero, frame_state, "skill_1")

    def enemy_1_in_main_hero_skill_2_atk_range(self, hero, frame_state):
        """敵方英雄是否在技能2攻擊範圍內"""
        return self._check_enemy_in_range(hero, frame_state, "skill_2")

    def enemy_1_in_main_hero_skill_3_atk_range(self, hero, frame_state):
        """敵方英雄是否在技能3攻擊範圍內"""
        return self._check_enemy_in_range(hero, frame_state, "skill_3")

    def enemy_soldier_under_main_heros_atk(self, hero, frame_state):
        """
        簡化為一維：判斷是否有敵方小兵在英雄的攻擊範圍內。
        返回 [1.0] 如果有，否則返回 [0.0]。
        """
        # 如果英雄血量 <= 0，直接返回不在範圍內
        if hero["actor_state"]["hp"] <= 0:
            return [0.0]

        hero_location = hero["actor_state"]["location"]

        # 定義一個共同的攻擊範圍，因為原始代碼中所有技能範圍都相同
        # 如果未來技能範圍不同，這裡需要調整邏輯來決定「一維」代表哪個範圍
        common_attack_range = 8000 

        # 預設結果為不在範圍內
        result = [0.0]

        # 檢查敵方小兵
        for npc in frame_state.get("npc_states", []):
            # 判斷條件：小兵存活、類型是SOLDIER、且是敵方陣營
            if (npc.get("hp", 0) > 0 and
                npc.get("sub_type") == "ACTOR_SUB_SOLDIER" and
                npc.get("camp") != self.main_camp):

                npc_location = npc.get("location", {})
                if npc_location:
                    distance = self.cal_dist(hero_location, npc_location) * 100 # 假設這裡的100是單位轉換

                    # 只要有一個小兵在範圍內，就設置結果為1.0並立即返回
                    if distance <= common_attack_range:
                        return [1.0] # 找到一個符合條件的就直接返回

        # 如果循環結束都沒有找到符合條件的小兵，則返回預設的 [0.0]
        #print(f"enemy_soldier_under_main_heros_atk (simplified): {result}")
        return result


    def enemy_tower_under_main_heros_atk(self, hero, frame_state):
        """敵方塔是否在攻擊範圍內"""
        if hero["actor_state"]["hp"] <= 0:
            return [0.0]

        hero_location = hero["actor_state"]["location"]
        attack_range = 8800  # 統一攻擊範圍為8000

        # 檢查敵方塔
        for npc in frame_state.get("npc_states", []):
            if (npc.get("hp", 0) > 0 and
                npc.get("sub_type") == "ACTOR_SUB_TOWER" and
                npc.get("camp") != self.main_camp):

                npc_location = npc.get("location", {})
                if npc_location:
                    distance = self.cal_dist(hero_location, npc_location) * 100
                    if distance <= attack_range:
                        return [1.0]
        #print(f"enemy_tower_under_main_heros_atk: {0.0}")
        return [0.0]

    def _find_nearest_enemy_bullet(self, hero, frame_state):
        """查找最近的敵方子彈"""
        hero_location = hero["actor_state"]["location"]
        nearest_bullet = None
        min_distance = float('inf')

        # 查找所有敵方子彈
        for bullet in frame_state.get("bullets", []):
            bullet_source_id = bullet.get("source_actor")

            # 判斷子彈是否為敵方
            is_enemy_bullet = False
            for enemy_hero in self.enemy_camp_hero_dict.values():
                if enemy_hero.get("actor_state", {}).get("runtime_id") == bullet_source_id:
                    is_enemy_bullet = True
                    break

            if is_enemy_bullet:
                bullet_location = bullet.get("location", {})
                if bullet_location:
                    distance = self.cal_dist(hero_location, bullet_location)
                    if distance < min_distance:
                        min_distance = distance
                        nearest_bullet = bullet
        
        return nearest_bullet

    def _check_enemy_in_range(self, hero, frame_state, range_type):
        """檢查敵方英雄是否在指定攻擊範圍內"""
        if hero["actor_state"]["hp"] <= 0:
            return [0.0]

        hero_location = hero["actor_state"]["location"]
        main_hero_attack_range = hero["actor_state"].get("attack_range", 0)

        # 獲取第一個敵方英雄
        enemy_heroes = list(self.enemy_camp_hero_dict.values())
        if not enemy_heroes:
            return [0.0]

        enemy_hero = enemy_heroes[0]
        if enemy_hero["actor_state"]["hp"] <= 0:
            return [0.0]

        enemy_location = enemy_hero["actor_state"]["location"]
        distance = self.cal_dist(hero_location, enemy_location) * 100
        #print(f"Hero distance: ", distance)
        # 根據範圍類型確定攻擊範圍 - 全部設置為8000
        if range_type == "common":
            attack_range = 8000
        elif range_type == "skill_1":
            attack_range = 8000  # 技能1範圍
        elif range_type == "skill_2":
            attack_range = 8000  # 技能2範圍
        elif range_type == "skill_3":
            attack_range = 8000  # 技能3範圍
        else:
            return [0.0]

        if distance <= attack_range:
            #print(f"enemy_in_{range_type}_range: {1.0}")
            return [1.0]
        else:
            return [0.0]
